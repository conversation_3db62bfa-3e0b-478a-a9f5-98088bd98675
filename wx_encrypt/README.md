# wx_encrypt - 微信小程序解密工具

这是一个用Go语言实现的微信小程序解密工具，可以解密加密的微信小程序包文件(.wxapkg)。

## 功能特性

- 自动检测文件是否需要解密
- 支持V1MMWX格式的加密小程序包
- 使用AES-CBC解密算法
- 支持PBKDF2密钥派生
- 命令行界面，使用简单
- 跨平台支持

## 安装

### 从源码编译

1. 确保已安装Go 1.21.13或更高版本
2. 克隆或下载源码
3. 在项目目录中运行：

```bash
go mod tidy
go build -o wx_encrypt
```

## 使用方法

### 基本用法

```bash
# 解密单个文件
./wx_encrypt /path/to/miniprogram.wxapkg

# 指定输出目录
./wx_encrypt -o /custom/output /path/to/miniprogram.wxapkg

# 显示帮助信息
./wx_encrypt -h

# 显示版本信息
./wx_encrypt -v
```

### 命令行选项

- `-h, --help`: 显示帮助信息
- `-v, --version`: 显示版本信息
- `-o, --output`: 指定输出目录（默认：./wxpack）

### 输出

解密成功后，文件将保存在`wxpack`目录中，文件名格式为`{小程序ID}.wxapkg`。

## 工作原理

1. **文件检测**: 检查文件头是否为"V1MMWX"来判断是否为加密文件
2. **ID提取**: 从文件路径中提取小程序ID
3. **密钥生成**: 使用PBKDF2算法生成AES解密密钥
4. **AES解密**: 解密文件的前1024字节数据
5. **XOR解密**: 对剩余数据进行XOR解密
6. **文件输出**: 将解密后的数据写入输出文件

## 技术细节

### 加密算法
- **AES-CBC**: 用于解密文件的主要内容
- **PBKDF2**: 用于从小程序ID生成解密密钥
- **XOR**: 用于解密文件的剩余部分

### 参数配置
- **Salt**: "saltiest"
- **IV**: "the iv: 16 bytes"
- **PBKDF2迭代次数**: 1000
- **密钥长度**: 32字节

## 注意事项

1. 确保有足够的磁盘空间存储解密后的文件
2. 解密后的文件保存在`wxpack`目录中，请勿删除该目录
3. 只支持V1MMWX格式的加密文件
4. 需要从微信小程序的安装路径中获取文件才能正确提取小程序ID

## 错误处理

程序会处理以下常见错误：
- 文件不存在
- 文件格式不正确
- 无法提取小程序ID
- 解密失败
- 文件写入失败

## 许可证

本项目基于原C#版本的功能实现，仅供学习和研究使用。

## 更新日志

### v1.0.0
- 初始版本
- 实现基本的解密功能
- 支持命令行操作
- 跨平台支持
