package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

const (
	version = "1.0.0"
	usage   = `wx_encrypt - 微信小程序解密工具

使用说明：
  选择小程序包进行解密，自动判断是否需要解密
  解密后放在wxpack文件夹，请勿删除该文件夹

用法:
  wx_encrypt [选项] <文件路径>

选项:
  -h, --help     显示帮助信息
  -v, --version  显示版本信息
  -o, --output   指定输出目录 (默认: ./wxpack)

示例:
  wx_encrypt /path/to/miniprogram.wxapkg
  wx_encrypt -o /custom/output /path/to/miniprogram.wxapkg
`
)

func main() {
	var (
		showHelp    = flag.Bool("h", false, "显示帮助信息")
		showVersion = flag.Bool("v", false, "显示版本信息")
		outputDir   = flag.String("o", "", "指定输出目录")
	)

	// 自定义usage
	flag.Usage = func() {
		fmt.Print(usage)
	}

	flag.Parse()

	// 处理帮助和版本选项
	if *showHelp {
		flag.Usage()
		return
	}

	if *showVersion {
		fmt.Printf("wx_encrypt version %s\n", version)
		return
	}

	// 检查是否提供了文件路径
	args := flag.Args()
	if len(args) == 0 {
		fmt.Println("错误: 请提供要解密的文件路径")
		fmt.Println("使用 -h 查看帮助信息")
		os.Exit(1)
	}

	filePath := args[0]

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Printf("错误: 文件不存在: %s\n", filePath)
		os.Exit(1)
	}

	// 创建解密器实例
	decryptor := NewWXDecryptor()

	// 如果指定了输出目录，更新解密器的输出路径
	if *outputDir != "" {
		decryptor.AppPath = *outputDir
	}

	// 显示程序信息
	fmt.Printf("wx_encrypt v%s - 微信小程序解密工具\n", version)
	fmt.Println("========================================")
	fmt.Println("使用说明：选择小程序包进行解密，自动判断是否需要解密")
	fmt.Println("解密后放在wxpack文件夹，请勿删除该文件夹")
	fmt.Println()

	// 执行解密
	err := decryptor.DecryptFile(filePath)
	if err != nil {
		fmt.Printf("解密失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()
	fmt.Println("操作完成！")
}

// 辅助函数：检查文件扩展名
func isValidFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	validExts := []string{".wxapkg", ".pkg"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	
	return false
}

// 辅助函数：格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
