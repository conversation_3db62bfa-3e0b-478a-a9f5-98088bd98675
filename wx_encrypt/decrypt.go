package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha1"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"time"

	"golang.org/x/crypto/pbkdf2"
)

// WXDecryptor 微信小程序解密器
type WXDecryptor struct {
	AppletPath string
	AppPath    string
}

// NewWXDecryptor 创建新的解密器实例
func NewWXDecryptor() *WXDecryptor {
	homeDir, _ := os.UserHomeDir()
	appletPath := filepath.Join(homeDir, "WeChat Files", "Applet")
	appPath, _ := os.Getwd()

	return &WXDecryptor{
		AppletPath: appletPath,
		AppPath:    appPath,
	}
}

// AESDecrypt AES解密函数
func (w *WXDecryptor) AESDecrypt(inputData, iv, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %v", err)
	}

	if len(inputData)%aes.BlockSize != 0 {
		return nil, fmt.Errorf("输入数据长度不是AES块大小的倍数")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(inputData))
	mode.CryptBlocks(decrypted, inputData)

	return decrypted, nil
}

// PBKDF2 密钥派生函数
func (w *WXDecryptor) PBKDF2(wxid, salt string) []byte {
	return pbkdf2.Key([]byte(wxid), []byte(salt), 1000, 32, sha1.New)
}

// FileContent 读取文件内容
func (w *WXDecryptor) FileContent(fileName string) ([]byte, error) {
	file, err := os.Open(fileName)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	return data, nil
}

// GetStr 从字符串中提取子字符串
func (w *WXDecryptor) GetStr(txtStr, firstStr, secondStr string) string {
	re := regexp.MustCompile("(?i)" + regexp.QuoteMeta(firstStr))
	parts := re.Split(txtStr, -1)

	if len(parts) < 2 {
		return ""
	}

	re2 := regexp.MustCompile("(?i)" + regexp.QuoteMeta(secondStr))
	parts2 := re2.Split(parts[1], -1)

	return parts2[0]
}

// GetTimeStamp 获取时间戳
func (w *WXDecryptor) GetTimeStamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

// WriteFile 写入文件
func (w *WXDecryptor) WriteFile(data []byte, fileName string) error {
	// 确保输出目录存在
	dir := filepath.Dir(fileName)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	file, err := os.Create(fileName)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	_, err = file.Write(data)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// DecryptFile 解密微信小程序文件
func (w *WXDecryptor) DecryptFile(fileName string) error {
	fmt.Printf("文件选择：%s\n", fileName)

	// 读取文件内容
	data, err := w.FileContent(fileName)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 检查文件头是否为加密文件
	if len(data) < 6 {
		return fmt.Errorf("文件太小，无法处理")
	}

	header := string(data[:6])
	if header != "V1MMWX" {
		fmt.Println("文件未加密，无需解密...")
		return nil
	}

	// 从文件路径提取小程序ID
	wxid := w.GetStr(fileName, "Applet"+string(filepath.Separator), string(filepath.Separator))
	if wxid == "" {
		return fmt.Errorf("小程序ID获取失败，无法解密")
	}

	fmt.Println("文件解密中...")

	// 解密参数
	salt := "saltiest"
	iv := []byte("the iv: 16 bytes")

	// 生成解密密钥
	key := w.PBKDF2(wxid, salt)

	// 提取需要解密的数据段
	if len(data) < 1030 {
		return fmt.Errorf("文件格式不正确")
	}

	encryptedData := data[6:1030] // 跳过6字节头部，取1024字节
	remainingData := data[1030:]  // 剩余数据

	// AES解密
	decryptedData, err := w.AESDecrypt(encryptedData, iv, key)
	if err != nil {
		return fmt.Errorf("AES解密失败: %v", err)
	}

	// XOR解密剩余数据
	lastChar := wxid[len(wxid)-2 : len(wxid)-1]
	xorKey := int(lastChar[0])

	var result []byte
	result = append(result, decryptedData[:1023]...)

	for _, b := range remainingData {
		result = append(result, byte(int(b)^xorKey))
	}

	// 生成输出文件名
	outFileName := filepath.Join(w.AppPath, "wxpack", wxid+".wxapkg")

	// 写入解密后的文件
	err = w.WriteFile(result, outFileName)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Printf("解密成功 -> %s\n", outFileName)
	return nil
}
